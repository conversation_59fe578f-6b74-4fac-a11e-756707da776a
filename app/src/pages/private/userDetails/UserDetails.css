@import url('../../../variables.css');

.user_details .card {
  background: var(--surface-card);
  border-radius: var(--border-radius);
  padding: var(--card-padding-screen);
  box-shadow: var(--card-shadow);
}

/* Status badges */
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: var(--font-semibold);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.enabled {
  background-color: var(--green-100);
  color: var(--green-800);
  border: 1px solid var(--green-200);
}

.status-badge.disabled {
  background-color: var(--red-100);
  color: var(--red-800);
  border: 1px solid var(--red-200);
}

/* Role badges */
.role-badge {
  padding: 0.25rem 0.5rem;
  border-radius: var(--border-radius);
  font-size: 0.75rem;
  font-weight: var(--font-medium);
  background-color: var(--blue-100);
  color: var(--blue-800);
  border: 1px solid var(--blue-200);
}

/* Cell content */
.department-cell,
.designation-cell {
  color: var(--text-primary);
}

.department-cell:empty::after,
.designation-cell:empty::after {
  content: 'No description';
  color: var(--text-secondary);
  font-style: italic;
}

/* Confirmation modal */
.confirmation-content {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.confirmation-content i {
  margin-right: 0.75rem;
}

/* Action buttons */
.p-button-danger {
  background-color: var(--red-500);
  border-color: var(--red-500);
}

.p-button-danger:hover {
  background-color: var(--red-600);
  border-color: var(--red-600);
}

.p-button-warning {
  background-color: var(--orange-500);
  border-color: var(--orange-500);
}

.p-button-warning:hover {
  background-color: var(--orange-600);
  border-color: var(--orange-600);
}

.p-button-success {
  background-color: var(--green-500);
  border-color: var(--green-500);
}

.p-button-success:hover {
  background-color: var(--green-600);
  border-color: var(--green-600);
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .user_details .card {
    padding: var(--card-padding-mobile);
  }
  
  .confirmation-content {
    flex-direction: column;
    text-align: center;
  }
  
  .confirmation-content i {
    margin-right: 0;
    margin-bottom: 0.5rem;
  }
}
