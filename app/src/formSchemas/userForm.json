{"fields": [{"type": "text", "name": "name", "label": "Full Name", "placeholder": "Enter full name", "icon": "pi-user", "validation": {"required": true, "minLength": 2, "maxLength": 50}}, {"type": "text", "name": "email", "label": "Email Address", "placeholder": "Enter email address", "icon": "pi-envelope", "validation": {"required": true, "email": true, "maxLength": 100}}, {"type": "password", "name": "password", "label": "Password", "placeholder": "Enter password", "icon": "pi-lock", "validation": {"required": true, "minLength": 8, "maxLength": 100, "pattern": "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[@#$%^&+=])(?=\\S+$).{8,}$"}}, {"type": "text", "name": "phoneNumber", "label": "Phone Number", "placeholder": "Enter phone number", "icon": "pi-phone", "validation": {"required": true, "maxLength": 20}}, {"type": "select", "name": "role", "label": "Role", "placeholder": "Select role", "icon": "pi-shield", "validation": {"required": true}, "options": [{"label": "User", "value": "ROLE_USER"}, {"label": "Admin", "value": "ROLE_ADMIN"}, {"label": "Super Admin", "value": "ROLE_SUPER_ADMIN"}]}, {"type": "select", "name": "departmentId", "label": "Department", "placeholder": "Select department", "icon": "pi-sitemap", "validation": {"required": true}, "options": []}, {"type": "select", "name": "designationId", "label": "Designation", "placeholder": "Select designation", "icon": "pi-id-card", "validation": {"required": true}, "options": []}, {"type": "text", "name": "employeeId", "label": "Employee ID", "placeholder": "Enter employee ID (optional)", "icon": "pi-tag", "validation": {"required": false, "maxLength": 50}}, {"type": "date", "name": "joiningDate", "label": "Joining Date", "placeholder": "Select joining date", "icon": "pi-calendar", "validation": {"required": false}}, {"type": "checkbox", "name": "enabled", "label": "Account Enabled", "validation": {"required": false}}], "actions": [{"id": "submit", "type": "submit", "label": "Save User"}, {"id": "cancel", "type": "button", "label": "Cancel"}]}